//
//  FMCommonHttp.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/6.
//  Copyright © 2017年 sdcf. All rights reserved.
//  一些公共的请求方法

#import "FMCommonHttp.h"
#import "CustomIOSAlertView.h"
#import "FMProgressHUD.h"
#import "HttpRequestTool+Daka.h"
#import "HttpRequestTool+Course.h"
#import "YTGNormalWebVC.h"
#import "FMTaskConfigModel.h"

@interface FMCommonHttp()

@end

@implementation FMCommonHttp

// 关注投顾
+ (void)focusBtnWithButton:(UIButton *)button
                 bigCastId:(NSString *)bigCastId
                    roomId:(NSString *)roomId
              focusSuccess:(void(^)())focusSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    
    if ([[FMUserDefault getUserId] isEqualToString:bigCastId]) {
        [SVProgressHUD showErrorWithStatus:@"不能关注自己"];
        return;
    }
    
    WEAKSELF;
    [HttpRequestTool focusDakaWithUserId:bigCastId roomId:roomId start:^{
        button.userInteractionEnabled = NO;
    } failure:^{
        button.userInteractionEnabled = YES;
        
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        button.userInteractionEnabled = YES;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            [[FMUserDataSyncManager sharedManager] noticeDaka:bigCastId];
            [[NSNotificationCenter defaultCenter] postNotificationName:kFocusNumChanged object:@{@"dakaId":bigCastId,@"focus":@"1"}];
            focusSuccess();

            FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
            FMSubTask *task = userTaskProgress.taskDic[@(FMTaskTypeFirstSign)];
            if (task.isEnable && task.completeNum < task.taskNum) { // 任务未完成
                task.completeNum = task.taskNum;
                [FMUserDefault setArchiverData:userTaskProgress forKey:UserTaskProgressCacheKey];
                [PushMessageView showWithTitle:@"任务完成" message:@"任务完成！新手任务奖励已发放" noticeImage:nil sureTitle:@"更多任务" cancelTitle:@"确定" clickSure:^{
                    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
                    }];
                } clickCancel:^{
                }];
            } else {
                [FMHelper showChangeAlertViewBy:[__weakSelf getFocusView] buttonTitles:nil buttonOperation:nil completion:^(CustomIOSAlertView *alertView) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [alertView close];
                    });
                }];
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

// 取消关注
+ (void)UnfollowWithButton:(UIButton *)button bigCastId:(NSString *)bigCastId unfollowSuccess:(void(^)())unfollowSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    
    [HttpRequestTool cancelFocusDakaWithUserId:bigCastId start:^{
        button.userInteractionEnabled = NO;
    } failure:^{
        button.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        button.userInteractionEnabled = YES;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"取消关注成功"];
            [[FMUserDataSyncManager sharedManager] unnoticeDaka:bigCastId];
            [[NSNotificationCenter defaultCenter] postNotificationName:kFocusNumChanged object:@{@"dakaId":bigCastId,@"focus":@"0"}];
            unfollowSuccess();
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - 收藏
+ (void)collectBtnWithButton:(UIView *)button contentId:(NSString *)contentId type:(NSString *)type collectSuccess:(void(^)())collectSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    
    if ([type isEqualToString:@"1"]) { // 问股
        [HttpRequestTool questionCollectWithQuestionId:contentId start:[self collectStartBlock:button] failure:[self collectFailureBlock:button] success:[self collectSuccessBlock:button collectSuccess:collectSuccess]];
    } else if ([type isEqualToString:@"2"]) { // 笔记
        [HttpRequestTool noteCollectWithNoteId:contentId start:[self collectStartBlock:button] failure:[self collectFailureBlock:button] success:[self collectSuccessBlock:button collectSuccess:collectSuccess]];
    }
}

#pragma mark - 课程/系列课程 收藏
+ (void)collectBtnWithButton:(UIView *)button contentId:(NSString *)contentId courseType:(CourseType)courseType collectSuccess:(void(^)())collectSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    [HttpRequestTool courseCollectWithCourseId:contentId CourseType:courseType start:[self collectStartBlock:button] failure:[self collectFailureBlock:button] success:[self collectSuccessBlock:button collectSuccess:collectSuccess]];
}

+ (void(^)())collectStartBlock:(UIView *)clickView  {
    return ^{
        clickView.userInteractionEnabled = NO;
        [SVProgressHUD show];
    };
}

+ (void(^)())collectFailureBlock:(UIView *)clickView {
    return ^{
        clickView.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    };
}

+ (void(^)(NSDictionary *dic))collectSuccessBlock:(UIView *)clickView collectSuccess:(void(^)())collectSuccess {
    return ^(NSDictionary *dic) {
        clickView.userInteractionEnabled = YES;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            collectSuccess();
            [SVProgressHUD dismiss];
            [[NSNotificationCenter defaultCenter] postNotificationName:kCollectNumChanged object:nil];
            
            [FMHelper showChangeAlertViewBy:[self getCollectionView] buttonTitles:nil buttonOperation:nil completion:^(CustomIOSAlertView *alertView) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [alertView close];
                });
            }];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    };
}

#pragma mark - 取消收藏
+ (void)unCollectWithButton:(UIView *)button contentId:(NSString *)contentId type:(NSString *)type unCollectSuccess:(void(^)())unCollectSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    
    if ([type isEqualToString:@"1"]) { // 问股
        [HttpRequestTool cancelQuestionCollectWithQuestionId:contentId start:[self cancelCollectStartBlock:button] failure:[self cancelCollectFailureBlock:button] success:[self cancelCollectSuccessBlock:button uncollectSuccess:unCollectSuccess]];
    } else if ([type isEqualToString:@"2"]) { // 笔记
        [HttpRequestTool cancelNoteCollectWithNoteId:contentId start:[self cancelCollectStartBlock:button] failure:[self cancelCollectFailureBlock:button] success:[self cancelCollectSuccessBlock:button uncollectSuccess:unCollectSuccess]];
    }
}

#pragma mark - 课程/系列课程 收藏
+ (void)unCollectWithButton:(UIView *)button contentId:(NSString *)contentId courseType:(CourseType)courseType unCollectSuccess:(void(^)())unCollectSuccess{
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    [HttpRequestTool courseCancelCollectWithCourseId:contentId CourseType:courseType start:[self cancelCollectStartBlock:button] failure:[self cancelCollectFailureBlock:button] success:[self cancelCollectSuccessBlock:button uncollectSuccess:unCollectSuccess]];
}

+ (void(^)())cancelCollectStartBlock:(UIView *)clickView  {
    return ^{
        clickView.userInteractionEnabled = NO;
        [SVProgressHUD show];
    };
}

+ (void(^)())cancelCollectFailureBlock:(UIView *)clickView {
    return ^{
        clickView.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    };
}

+ (void(^)(NSDictionary *dic))cancelCollectSuccessBlock:(UIView *)clickView uncollectSuccess:(void(^)())uncollectSuccess {
    return ^(NSDictionary *dic) {
        clickView.userInteractionEnabled = YES;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            uncollectSuccess();
            [SVProgressHUD showSuccessWithStatus:@"取消收藏成功"];
            [[NSNotificationCenter defaultCenter] postNotificationName:kCollectNumChanged object:nil];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    };
}

+ (UIView *)getFocusView {
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 300*UI_RELATIVE_WIDTH, 150*UI_RELATIVE_WIDTH)];
    view.backgroundColor = UIColor.up_contentBgColor;
    view.layer.cornerRadius = 7.0f;
    UIImageView *imgV = [UIImageView new];
    [view addSubview:imgV];
    [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@(51*UI_RELATIVE_WIDTH));
        make.centerX.equalTo(view);
        make.top.equalTo(@25);
    }];
    imgV.image = [UIImage imageNamed:@"gzcg"];
    
    UILabel *desLabel = [UILabel new];
    [view addSubview:desLabel];
    [desLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imgV.mas_bottom).offset(15);
        make.centerX.equalTo(imgV);
    }];
    desLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
    desLabel.font = [UIFont systemFontOfSize:20.0f];
    desLabel.text = @"关注成功";
    return view;
}

+ (UIView *)getCollectionView {
    UIView *view = [[UIView alloc] init];
    view.frame = CGRectMake(0, 0, 300*UI_RELATIVE_WIDTH, 160*UI_RELATIVE_WIDTH);
    UIImageView *imgV = [UIImageView new];
    [view addSubview:imgV];
    [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@(51*UI_RELATIVE_WIDTH));
        make.centerX.equalTo(view);
        make.top.equalTo(@25);
    }];
    imgV.image = [UIImage imageNamed:@"sccg"];
    
    UILabel *desLabel = [UILabel new];
    [view addSubview:desLabel];
    [desLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imgV.mas_bottom).offset(15);
        make.centerX.equalTo(imgV);
    }];
    desLabel.textColor = FMZeroColor;
    desLabel.font = [UIFont systemFontOfSize:20.0f];
    desLabel.text = @"收藏成功";
    
    UILabel *desLabel2 = [UILabel new];
    [view addSubview:desLabel2];
    [desLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(desLabel.mas_bottom).offset(10);
        make.centerX.equalTo(imgV);
    }];
    desLabel.textColor = ColorWithHex(0x666666);
    desLabel.font = [UIFont systemFontOfSize:15.0f];
    desLabel2.text = @"请到个人中心查看";
    
    return view;
}

@end
